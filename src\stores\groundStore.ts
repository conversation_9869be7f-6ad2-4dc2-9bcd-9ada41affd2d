import { InventoryItemStack, Item } from "src/Interfaces";
import { mapSize } from "src/settings";
import { create } from "zustand";
import { v4 as uuidv4 } from 'uuid';

interface GroundStore {
    allRegionGroundStacks: InventoryItemStack[][];

    addOneToGroundStacks: (regionIndex: number, itemDef: Item) => void;
    addItemDefsToGroundStacks: (regionIndex: number, itemDefs: Item[]) => void; // preferred

    addStackToGroundStacks: (regionIndex: number, itemStack: InventoryItemStack) => void;
    addStacksToGroundStacks: (regionIndex: number, itemStacks: InventoryItemStack[]) => void; // preferred

    removeStackByIndex: (regionIndex: number, stackIndex: number) => void;
    removeStackByUuid: (regionIndex: number, uuid: string) => void;
    swapStacks: (regionIndex: number, fromIndex: number, toIndex: number) => void;
    addStackByIndex: (regionIndex: number, stackIndex: number, itemStack: InventoryItemStack) => void;

    
    allRegionGroundBuildings: InventoryItemStack[][];
    addToGroundBuildings: (regionIndex: number, itemDef: Item) => void;
    swapGroundBuildings: (regionIndex: number, fromIndex: number, toIndex: number) => void;
}

export const useGroundStore = create<GroundStore>((set, get) => ({
    allRegionGroundStacks: new Array(mapSize['medium']).fill(null).map(() => new Array(5)),
    allRegionGroundBuildings: new Array(mapSize['medium']).fill(null).map(() => new Array(5)),

    addToGroundBuildings: (regionIndex: number, itemDef: Item) => set((state) => {
        const newAllRegionGroundBuildings = [...state.allRegionGroundBuildings];
        const groundBuildings = newAllRegionGroundBuildings[regionIndex];
        const itemStack = {
            itemId: itemDef.id,
            quantity: 1,
            uuid: uuidv4(),
            itemDef: itemDef
        };
        if (groundBuildings == null) {
            newAllRegionGroundBuildings[regionIndex] = [itemStack];
        } else {
            //  try to find an empty slot
            let foundEmptySlot = false;
            for (let i = 0; i < groundBuildings.length; i++) {
                if (!groundBuildings[i]) {
                    groundBuildings[i] = itemStack;
                    newAllRegionGroundBuildings[regionIndex] = [...groundBuildings];
                    foundEmptySlot = true;
                    break;
                }
            }
            if (!foundEmptySlot) {
                newAllRegionGroundBuildings[regionIndex] = [...groundBuildings, itemStack];
            }
        }
        return { allRegionGroundBuildings: newAllRegionGroundBuildings };
    }),

    swapGroundBuildings: (regionIndex: number, fromIndex: number, toIndex: number) => set((state) => {
        const newAllRegionGroundBuildings = [...state.allRegionGroundBuildings];
        const newGroundBuildings = [...newAllRegionGroundBuildings[regionIndex]];
        const temp = newGroundBuildings[fromIndex];
        newGroundBuildings[fromIndex] = newGroundBuildings[toIndex];
        newGroundBuildings[toIndex] = temp;
        newAllRegionGroundBuildings[regionIndex] = newGroundBuildings;
        
        return { allRegionGroundBuildings: newAllRegionGroundBuildings };
    }),

    // initializeRegion: (regionIndex) => set((state) => {
    //     const allRegionGroundStacks = [];
    //     return { allRegionGroundStacks: newPlacedBuildingsInAllRegions };
    // }),

    addOneToGroundStacks: (regionIndex: number, itemDef: Item) => {
        const { addStackToGroundStacks } = get();
        
        const itemStack: InventoryItemStack = {
            itemId: itemDef.id,
            freshness: itemDef.freshness,
            quantity: itemDef.quantityMod ? itemDef.quantityMod : 1,
            uuid: uuidv4(),
            itemDef: itemDef
        };
        addStackToGroundStacks(regionIndex, itemStack);
    },

    addItemDefsToGroundStacks: (regionIndex: number, itemDefs: Item[]) => {
        const { addStacksToGroundStacks } = get();

        const itemIdAndStackMap: Map<string, InventoryItemStack> = new Map();
        itemDefs.forEach((itemDef) => {
            const addQuantity = itemDef.quantityMod ? itemDef.quantityMod : 1;
            if (itemIdAndStackMap.has(itemDef.id)) {
                itemIdAndStackMap.get(itemDef.id).quantity += addQuantity;
                return;
            } else {
                itemIdAndStackMap.set(itemDef.id, {
                    itemId: itemDef.id,
                    freshness: itemDef.freshness,
                    quantity: addQuantity,
                    uuid: uuidv4(),
                    itemDef: itemDef
                });
            }
        });
        addStacksToGroundStacks(regionIndex, [...itemIdAndStackMap.values()]);
    },

    addStackToGroundStacks: (regionIndex: number, itemStack: InventoryItemStack) => set((state) => {
        const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
        const groundStacks = newAllRegionGroundStacks[regionIndex];
        if (groundStacks == null) {
            newAllRegionGroundStacks[regionIndex] = [itemStack];
        } else {
            //  try to find an empty slot
            let foundEmptySlot = false;
            for (let i = 0; i < groundStacks.length; i++) {
                if (!groundStacks[i]) {
                    groundStacks[i] = itemStack;
                    newAllRegionGroundStacks[regionIndex] = [...groundStacks];
                    foundEmptySlot = true;
                    break;
                }
            }
            if (!foundEmptySlot) {
                newAllRegionGroundStacks[regionIndex] = [...groundStacks, itemStack];
            }
        }
        return { allRegionGroundStacks: newAllRegionGroundStacks };
    }),

    addStacksToGroundStacks: (regionIndex: number, itemStacks: InventoryItemStack[]) => set((state) => {
        const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
        const groundStacks = newAllRegionGroundStacks[regionIndex];
        if (groundStacks == null) {
            newAllRegionGroundStacks[regionIndex] = [...itemStacks];
        } else {
            //  try to find an empty slot
            let allPlaced = false;
            let itemStacksIndex = 0;
            for (let i = 0; i < groundStacks.length; i++) {
                if (!groundStacks[i]) {
                    groundStacks[i] = itemStacks[itemStacksIndex];
                    itemStacksIndex++;
                    if (itemStacksIndex >= itemStacks.length) {
                        newAllRegionGroundStacks[regionIndex] = [...groundStacks];
                        allPlaced = true;
                        break;
                    }
                }
            }
            if (!allPlaced) {
                newAllRegionGroundStacks[regionIndex] = [...groundStacks, ...itemStacks.slice(itemStacksIndex)];
            }
        }
        return { allRegionGroundStacks: newAllRegionGroundStacks };
    }),

    addStackByIndex: (regionIndex: number, stackIndex: number, itemStack: InventoryItemStack) => set((state) => {
        
        const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
        const newGroundStacks = [...newAllRegionGroundStacks[regionIndex]];
        newGroundStacks[stackIndex] = itemStack;
        newAllRegionGroundStacks[regionIndex] = newGroundStacks;
        
        return { allRegionGroundStacks: newAllRegionGroundStacks };
    }),

    removeStackByIndex: (regionIndex: number, stackIndex: number) => set((state) => {
        const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
        const newGroundStacks = [...newAllRegionGroundStacks[regionIndex]];
        newAllRegionGroundStacks[regionIndex] = newGroundStacks.splice(stackIndex, 1);
        
        return { allRegionGroundStacks: newAllRegionGroundStacks };
    }),

    removeStackByUuid: (currRegionIndex: number, uuid: string) => set((state) => {
        const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
        const newGroundStacks = [...newAllRegionGroundStacks[currRegionIndex]];
        const stackIndex = newGroundStacks.findIndex(stack => stack?.uuid === uuid);
        newGroundStacks[stackIndex] = null;
        newAllRegionGroundStacks[currRegionIndex] = newGroundStacks;
        
        return { allRegionGroundStacks: newAllRegionGroundStacks };
    }),

    swapStacks: (regionIndex: number, fromIndex: number, toIndex: number) => set((state) => {
        const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
        const newGroundStacks = [...newAllRegionGroundStacks[regionIndex]];
        const temp = newGroundStacks[fromIndex];
        newGroundStacks[fromIndex] = newGroundStacks[toIndex];
        newGroundStacks[toIndex] = temp;
        newAllRegionGroundStacks[regionIndex] = newGroundStacks;
        
        return { allRegionGroundStacks: newAllRegionGroundStacks };
    }),

    // tryConsumeItemsFromGroundStacks: (regionIndex: number, itemsToRemove: ItemToConsume[]) => {
    //     let success = false;
        
    //     set((state) => {
    //         const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
    //         const groundStacks = newAllRegionGroundStacks[regionIndex];
    //         if (groundStacks == null) {
    //             return { allRegionGroundStacks: newAllRegionGroundStacks };
    //         } else {
    //             const newGroundStacks = groundStacks.filter(stack => !itemStacks.find(itemStack => itemStack.uuid === stack.uuid));
    //             newAllRegionGroundStacks[regionIndex] = newGroundStacks;
    //             return { allRegionGroundStacks: newAllRegionGroundStacks };
    //         }
    //     })
    // },
}));
