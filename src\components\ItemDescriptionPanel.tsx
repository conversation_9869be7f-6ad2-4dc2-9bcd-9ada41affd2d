import React = require("react");
import { ItemLocation, ResourceTypes, statsIconMap } from "src/enums/common_enum";
import { ItemIcon } from "./common";
import { useEquipmentStore } from "src/stores/equipmentStore";
import { useRootStore } from "src/stores/rootStore";
import { getText } from "src/i18n";
import { startProgressBar } from "src/gameinfo";
import { InventoryItemStack } from "src/Interfaces";
import { useGroundStore } from "src/stores/groundStore";
import { MacOSModal } from "./WindowManagement/MacOSModal";


function getSignFromNumber(num: number): string {
    return num >= 0 ? '+' : '-';
}

interface StatLineProps {
    stat: number;
    type: string;
}

const StatLine: React.FC<StatLineProps> = ({stat, type}) => {
    if (!stat) {
        return null;
    }

    return (
        <div className="stat-line">
            <ItemIcon itemDef={{icon: statsIconMap[type]}} maxWidth={18} invertColor={true} />
            {getSignFromNumber(stat)}{stat}
            <br/>
        </div>
    );
};

interface ItemDescriptionPanelProps {
    selectedStack: InventoryItemStack;
    onClose?: () => void;
    showActions?: boolean;
    location: ItemLocation;
}

export const ItemDescriptionPanel: React.FC<ItemDescriptionPanelProps> = ({
    selectedStack,
    onClose,
    showActions = false,
    location
}) => {
    const removeItemsFromInventory = useRootStore(state => state.removeItemsFromInventory);

    const itemDef = selectedStack.itemDef;

    // Handle eat button click
    const handleEat = () => {
        // Implement eat functionality here
        console.log("Eating", selectedStack.itemDef.name);
        startProgressBar(getText("Eating..."), 5, () => {
            removeItemsFromInventory([{itemDef: selectedStack.itemDef, uuid: selectedStack.uuid, quantity: 1}]);
        })
        if (onClose) onClose();
    };

    return (
        <>
            <div className="selected-resource-header">
                <ItemIcon itemDef={itemDef} />
                <span className="resource-name">{itemDef.name}</span>
            </div>
            <div className="resource-type">
                {itemDef.type.name} {itemDef.type.icon}
            </div>
            <div className="resource-stats">
                {itemDef.description || 'No description available'}
            </div>

            {/* Show equipment slot type if it's an equipable item */}
            {itemDef.type.id === 'Equipable' && itemDef.slotType && (
                <div className="equipment-info">
                    <div className="slot-type">
                        <span className="slot-icon">{itemDef.slotType.icon}</span>
                        <span className="slot-name">{getText("Slot")}: {itemDef.slotType.name}</span>
                    </div>
                </div>
            )}

            {/* Show food stats if it's an edible item */}
            {(itemDef.type === ResourceTypes.EDIBLE || itemDef.type === ResourceTypes.MEDICINAL) &&
                <div>
                    <StatLine stat={itemDef.food} type="food" />
                    <StatLine stat={itemDef.water} type="water" />
                    <StatLine stat={itemDef.energy} type="energy" />
                    <StatLine stat={itemDef.health} type="health" />
                </div>
            }

            {/* Action buttons */}
            {showActions && (
                <div className="item-actions">
                    {/* Equip button for equipable items */}
                    {itemDef.type.id === 'Equipable' && (
                        <EquipButton selectedStack={selectedStack} onClose={onClose} />
                    )}

                    {/* Eat button for food items */}
                    {itemDef.type === ResourceTypes.EDIBLE && (
                        <button className="panel-btn" onClick={handleEat}>
                            <ItemIcon itemDef={{icon: 'images/ui_icons/eat.png'}} maxWidth={18} />
                            {getText("Eat")}
                        </button>
                    )}

                    {(ItemLocation.Ground === location) &&
                        <PickUpButton selectedStack={selectedStack} onClose={onClose} />
                    }

                    {(ItemLocation.Inventory === location) &&
                        <DropButton selectedStack={selectedStack} onClose={onClose} />
                    }
                    
                </div>
            )}
        </>
    );
};


const EquipButton = (props: {
    selectedStack: InventoryItemStack,
    onClose: () => void
}) => {
    const [ isEquipFailed, setIsEquipFailed ] = React.useState(false);
    const equipItem = useEquipmentStore(state => state.equipItem);
    const itemStacks = useRootStore(state => state.itemStacks);
    const itemDef = props.selectedStack.itemDef;

    // Handle equip button click
    const handleEquip = () => {
        if (itemDef.type.id === 'Equipable' && itemDef.slotType) {
            // Find the item in inventory
            const itemStack = itemStacks.find(stack =>
                stack && stack.itemId === itemDef.id
            );

            if (itemStack) {
                const success = equipItem(itemStack, itemDef.slotType.id);
                if (!success) {
                    setIsEquipFailed(true);
                } else {
                    props.onClose();
                }
            }
        }
    };
    return (
        <>
            <button className="panel-btn" onClick={handleEquip}>
                <ItemIcon itemDef={{icon: 'images/ui_icons/tshirt.png'}} maxWidth={18} />
                {getText("Equip")}
            </button>
            <MacOSModal
                title={getText('Equip Failed')}
                isOpen={isEquipFailed}
                onClose={props.onClose}
                initialSize={{ width: 600, height: 700 }}
                // portalId={interactionModalId.current}
            >
                {getText("You already equipped an item in this slot.")}
            </MacOSModal>
        </>
    );
};

const PickUpButton = (props: {
    selectedStack: InventoryItemStack,
    onClose: () => void
}) => {
    const addStackToInventory = useRootStore(state => state.addStackToInventory);
    const currRegionIndex = useRootStore(state => state.currRegionIndex);
    const removeStackByUuid = useGroundStore(state => state.removeStackByUuid);

    const handlePickUp = () => {
        console.log("handlePickUp", props.selectedStack);
        addStackToInventory(props.selectedStack);
        removeStackByUuid(currRegionIndex, props.selectedStack.uuid);
        props.onClose();
    }

    return (
        <button className="panel-btn" onClick={handlePickUp}>
            <ItemIcon itemDef={{icon: 'images/ui_icons/pick_up.png'}} maxWidth={18} />
            {getText("Pick Up")}
        </button>
    );
};

const DropButton = (props: {
    selectedStack: InventoryItemStack,
    onClose: () => void
}) => {
    const removeSelectedInventoryStackByUuid = useRootStore(state => state.removeSelectedInventoryStackByUuid);
    const currRegionIndex = useRootStore(state => state.currRegionIndex);
    const addStackToGroundStacks = useGroundStore(state => state.addStackToGroundStacks);

    const handleDrop = () => {
        console.log("handleDrop", props.selectedStack);
        addStackToGroundStacks(currRegionIndex, props.selectedStack);
        removeSelectedInventoryStackByUuid(props.selectedStack.uuid);
        props.onClose();
    }

    return (
        <button className="panel-btn" onClick={handleDrop}>
            <ItemIcon itemDef={{icon: 'images/ui_icons/drop.png'}} maxWidth={18} />
            {getText("Drop")}
        </button>
    );
};
