
import React = require("react");
import { Items, UnknownItem } from "src/enums/resources";
import { ItemDescriptionPanel } from "./ItemDescriptionPanel";
import { startProgressBar } from "src/gameinfo";
import { ItemIcon } from "./common";
import { getText } from "src/i18n";
import { ResourceMetaInfo } from "src/Interfaces";
import { FishingButton } from "./FishingButton";
import { useRootStore } from "../stores/rootStore";
import { GroundBuildingsGrid, GroundStorageGrid } from "./GroundGrid";
import { showFadedOverlay, showPromptOverlay } from "src/util";
import { MacOSModal } from "./WindowManagement/MacOSModal";
import { BuildList } from "./BuildList";
import { useGroundStore } from "src/stores/groundStore";

const ResourceIcon = ({resource}) => {
    return (
        <div className={`resource-icon-inner ${resource.rarity}`}>
            <ItemIcon itemDef={resource} />
        </div>
    )
}

const ResourcesPanel = (props : {
    resourceMetaInfos: ResourceMetaInfo[],
}) => {
    const [selectedResource, setSelectedResource] = React.useState(null);

    // Check if resourceMetaInfos is undefined or null
    const metaInfos = props.resourceMetaInfos || [];

    return (
        <>
            <div className="resource-grid-container">
                <div className="subtitle-label">
                    <div>
                        <img src = "images/ui_icons/cube.svg" className="resource-cube-icon" />
                        {getText("AVAILABLE RESOURCES")}
                    </div>
                </div>
                <div className="scrollable-container resource-grid">
                    {metaInfos.length === 0 && <p className="no-resources">No resources available in this area</p>}
                    {metaInfos.map((resourceMeta, index) => {
                        const resource = resourceMeta.discovered? Items[resourceMeta.id] : UnknownItem;
                        return (
                            <div key={index}
                                className={`resource-icon ${selectedResource?.id === resourceMeta.id ? 'selected' : ''}`}
                                onClick={() => setSelectedResource(resource)}>
                                <ResourceIcon resource={resource}/>
                            </div>
                        )
                    })}
                </div>
            </div>

            {selectedResource &&
                <div id="selectedResourceInfo" className="scrollable-container">
                    <ItemDescriptionPanel selectedStack={{itemDef: selectedResource}} />
                </div>
            }
        </>
    )
};

export const EnvironmentDisplay = React.memo((props : {
    terrainName: string,
}) => {
    const currRegionIndex = useRootStore(state => state.currRegionIndex);
    const resourceMetaInfosInAllRegions = useRootStore(state => state.resourceMetaInfosInAllRegions);
    // const resourcesMetaInfo = useRootStore(state => state.resourceMetaInfosInAllRegions[currRegionIndex]);
    // const biomesList = useRootStore(state => state.biomesList);


    // Add null check to ensure resourceMetaInfosInAllRegions and currRegionIndex are valid
    const resourcesMetaInfo: ResourceMetaInfo[] =
        resourceMetaInfosInAllRegions &&
        currRegionIndex !== undefined &&
        currRegionIndex !== null &&
        resourceMetaInfosInAllRegions[currRegionIndex]
            ? resourceMetaInfosInAllRegions[currRegionIndex]
            : [];

    console.log("ENviromentDisplay rerendered!!!");

    return (
        <div id="groundWrapper">
            <div id="environmentDisplay">
                <div className="subtitle-label">
                    <div>
                        <img src = "images/ui_icons/safari.svg" className="resource-cube-icon" />
                        {getText("AVAILABLE ACTIVITIES")}
                    </div>
                </div>
                <div id="actionBtnsContainer">
                    <ExploreBtn resourcesMetaInfo={resourcesMetaInfo} resourceMetaInfosInAllRegions={resourceMetaInfosInAllRegions} />
                    <FishingButton />
                    <BuildBtn />
                    <button className='panel-btn' >
                        <img src="images/ui_icons/tools.svg" width={14} height={14} style={{filter: 'invert(1)'}} />
                        <div className="panel-btn-text">{getText("Hunt")}</div>
                    </button>
                </div>
                <div id="resources-panel">
                    <ResourcesPanel
                        resourceMetaInfos={resourcesMetaInfo}
                    />
                </div>
            </div>

            <div id="inventoryDisplay">
                <GroundBuildingsGrid/>
                <GroundStorageGrid/>
            </div>

        </div>
   );
});

const BuildBtn = () => {

    const [openCraftModal, setOpenCraftModal] = React.useState(false);

    return <>
            <button className='panel-btn' onClick={() => setOpenCraftModal(true)}>
                <img src="images/ui_icons/building.png" width={14} height={14} style={{filter: 'invert(1)'}} />
                <div className="panel-btn-text">{getText("Build")}</div>
            </button>
            {openCraftModal && 
                <MacOSModal
                    title={getText('Build')}
                    isOpen={true}
                    onClose={() => setOpenCraftModal(false)}
                    initialSize={{ width: 600, height: 700 }}
                    // portalId={interactionModalId.current}
                >
                    <BuildList />
                </MacOSModal>
            }
    </>
}


const ExploreBtn = (props : {
    resourcesMetaInfo: ResourceMetaInfo[],
    resourceMetaInfosInAllRegions: ResourceMetaInfo[][],
}) => {

    const currRegionIndex = useRootStore(state => state.currRegionIndex);     // Get the current region index
    const setResourceMetaInfosInAllRegions = useRootStore(state => state.setResourceMetaInfosInAllRegions);
    const addItemDefsToGroundStacks = useGroundStore(state => state.addItemDefsToGroundStacks);

    const exploreBiome = () => {
        if (!props.resourcesMetaInfo || props.resourcesMetaInfo.length === 0) {
            console.warn("No resources available to explore");
            return;
        }

        startProgressBar("Exploring", 60, () => {
            let hasNewDiscovery = false;
            const discoveredResourceMetas = []
            for (let i = 0; i < 3; i++) {
                const discoveredResourceMeta = props.resourcesMetaInfo[Math.floor(Math.random() * props.resourcesMetaInfo.length)];
                discoveredResourceMetas.push(discoveredResourceMeta);
                // console.log("discoveredResourceMeta", discoveredResourceMeta);
                if (discoveredResourceMeta && !discoveredResourceMeta.discovered) {
                    discoveredResourceMeta.discovered = true;
                    hasNewDiscovery = true;
                }
            }

            if (hasNewDiscovery) {
                setResourceMetaInfosInAllRegions([...props.resourceMetaInfosInAllRegions]);
            }

            // if (discoveredResourceMeta && discoveredResourceMeta.id) {
            console.log("Exploring... discoveredResourceMetas", discoveredResourceMetas);
            const itemDefs = discoveredResourceMetas.map((resourceMeta) => Items[resourceMeta.id]);
            addItemDefsToGroundStacks(currRegionIndex, itemDefs);
            for (const itemDef of itemDefs) {
                const addQuantity = itemDef.quantityMod ? itemDef.quantityMod : 1;
                // showFadedOverlay(`+ ${itemDef.name} x${addQuantity}`);
                showPromptOverlay(`+ ${itemDef.name} x${addQuantity}`);
            }
        })
    }

    return (
        <button className="panel-btn" onClick={exploreBiome}>
            <img src="images/ui_icons/search.svg" width={14} height={14} style={{filter: 'invert(1)'}} />
            <div className="panel-btn-text">{getText("Search")}</div>
        </button>
    )
}

// const ActivityBtn = (onClick, label) => {
//     return (
//         <button className="panel-btn" onClick={onClick}>
//             <img src="images/ui_icons/search.svg" width={14} height={14} style={{filter: 'invert(1)'}} />
//             <div className="panel-btn-text">{label}</div>
//         </button>
//     );
// }
