// @ts-check

console.log("Loading resources.js");
import { getText } from '../i18n.js';
import { ResourceTypes, RARITY } from './common_enum.js';
import { FOOD, MUSHROOMS, VEGETABLES } from './food_enums.js';
import { CraftableItem, Item, ResourceMetaInfo } from 'src/Interfaces.js';
import { MATERIALS } from './Materials.js';
import { CRAFTABLE_EQUIPABLES, CRAFTABLE_MATERIALS, CRAFTABLE_TOOLS, CRAFTABLE_TRANSPORTS, CRAFTABLES } from './CRAFTABLE_enums.js';
import { BUILDINGS, CRAFTABLE_BUILDINGS } from './building_enums_new.js';


/**
 * jsdoc:
 * @typedef {Object} ResourceType
 * @property {string} id - The unique identifier for the resource type
 * @property {string} name - The name of the resource type
 * @property {string} icon - The icon of the resource type
 * @property {string} description - The description of the resource typ
 * @property {?string} [invAction] - The action to perform on the resource type
 *
 * @typedef {Object} Ingredient
 * @property {string} id - The unique identifier for the ingredient
 * @property {number} quantity - The quantity of the ingredient
 *
 * @typedef {Object} Item
 * @property {string} id - The unique identifier for the item
 * @property {string} name - The name of the item
 * @property {string} description - The description of the item
 * @property {ResourceType} type - The type of the item
 * @property {string} rarity - The rarity of the item
 * @property {string} icon - The icon of the item
 * @property {?number} [freshness] - The freshness value of the item, represented as in game mins. 1 day = 1440 mins.
 * @property {number} [quantityMod] - The quantity modifier of the item
 * @property {?number} [durability] - The durability of the item
 * @property {?boolean} [isFishingRod] - Whether the item is a fishing rod
 * @property {number} [gatherTime] - The in game minutes it takes to gather the item
 *
 *
 * @typedef {Item & {
 *   ingredients?: Ingredient[]
 *   time?: number
 * }} CraftableItem
 * @property {Ingredient[]} ingredients - The ingredients needed to craft the item
 * @property {number} time - The time it takes to craft the item
 *
 * @typedef {Item & {
 *   time: number
 *   quantity: number
 * }} HarvestableItem
 * @property {number} time - The time it takes to harvest the item
 * @property {number} quantity - The quantity of the item
 *
 * @typedef {CraftableItem & {
 *   slotType: SlotType
 * }} EquipableItem
 *
 * @typedef {EquipableItem & {
 *   capacity: number
 * }} BackpackItem
 *
 *
 * @typedef {Item & {
 *   food?: number
 *   water?: number
 *   energy?: number
 *   health?: number
 * }} FoodItem
 */


export const HUNTABLES = {
    "bear": {
        "id": "bear",
        "name": "Bear",
        "type": ResourceTypes.HUNTABLE,
        "icon": "🐻",
        "biomes": ["TEMPERATE_RAIN_FOREST", "TEMPERATE_DECIDUOUS_FOREST", "GRASSLAND"],
        get description() { return getText("Bear"); }},
    "shark": {
        "id": "shark",
        "name": "Shark",
        "type": ResourceTypes.HUNTABLE,
        "icon": "🦈",
        "biomes": ["OCEAN"],
        get description() { return getText("Shark"); }
    },
}

export const UnknownItem: Item = {
        "id": "Unknown",
        "name": "Unknown",
        "type": ResourceTypes.UNKNOWN,
        "icon": "❓",
        get description() { return getText("desc_unknown"); },
};



export const CRAFTABLES_MAP: { [key: string]: { [key: string]: CraftableItem}} = {
    "Building": CRAFTABLE_BUILDINGS,
    "Equipable": CRAFTABLE_EQUIPABLES,
    // "Edible": CRAFTABLE_FOODS,
    "Tool": CRAFTABLE_TOOLS,
    "Material": CRAFTABLE_MATERIALS,
    "Transport": CRAFTABLE_TRANSPORTS,
};

const CRAFTABLES: { [key: string]: CraftableItem} = {
    ...CRAFTABLE_EQUIPABLES,
    ...CRAFTABLE_TOOLS,
    ...CRAFTABLE_MATERIALS,
    ...CRAFTABLE_TRANSPORTS,
    ...CRAFTABLE_BUILDINGS
};

export const Items: { [key: string]: Item } = {
    ...CRAFTABLES,
    ...HUNTABLES,
    ...MATERIALS,
    ...FOOD,
    ...UnknownItem,
    "AloeVera": {
        "id": "AloeVera",
        "name": "Aloe Vera",
        "type": ResourceTypes.MEDICINAL,
        "icon": "🌿",
        "health": 12,
        get description() { return getText("Aloe Vera"); }},

    // Ocean items
    "Starfish": {
        "id": "Starfish",
        "name": "Starfish",
        "type": ResourceTypes.MATERIAL,
        "icon": "⭐",
        "rarity": RARITY.UNCOMMON,
        get description() { return getText("Starfish"); }},
    "Jellyfish": {
        "id": "Jellyfish",
        "name": "Jellyfish",
        "type": ResourceTypes.EDIBLE,
        "icon": "🪼",
        "rarity": RARITY.RARE,
        "food": 8,
        "water": 5,
        "health": -2,
        get description() { return getText("Jellyfish"); }},
    "SeaGlass": {
        "id": "SeaGlass",
        "name": "Sea Glass",
        "type": ResourceTypes.MATERIAL,
        "icon": "💎",
        "rarity": RARITY.UNCOMMON,
        get description() { return getText("Sea Glass"); }},
    "Kelp": {
        "id": "Kelp",
        "name": "Kelp",
        "type": ResourceTypes.EDIBLE,
        "icon": "🌿",
        "rarity": RARITY.COMMON,
        "food": 5,
        "water": 8,
        "energy": 3,
        get description() { return getText("Kelp"); }},
    "OceanSalt": {
        "id": "OceanSalt",
        "name": "Ocean Salt",
        "type": ResourceTypes.MATERIAL,
        "icon": "🧂",
        "rarity": RARITY.COMMON,
        get description() { return getText("Ocean Salt"); }},
    "Plankton": {
        "id": "Plankton",
        "name": "Plankton",
        "type": ResourceTypes.EDIBLE,
        "icon": "🦠",
        "rarity": RARITY.COMMON,
        "food": 3,
        "water": 1,
        get description() { return getText("Plankton"); }},
    "Ambergris": {
        "id": "Ambergris",
        "name": "Ambergris",
        "type": ResourceTypes.VALUABLE,
        "icon": "💰",
        "rarity": RARITY.LEGENDARY,
        get description() { return getText("Ambergris"); }},
    "Barnacles": {
        "id": "Barnacles",
        "name": "Barnacles",
        "type": ResourceTypes.MATERIAL,
        "icon": "🐌",
        "rarity": RARITY.COMMON,
        get description() { return getText("Barnacles"); }},
    "SeaUrchin": {
        "id": "SeaUrchin",
        "name": "Sea Urchin",
        "type": ResourceTypes.EDIBLE,
        "icon": "🌑",
        "rarity": RARITY.UNCOMMON,
        "food": 10,
        "water": 2,
        "health": 5,
        get description() { return getText("Sea Urchin"); }},
    "CoralFragment": {
        "id": "CoralFragment",
        "name": "Coral Fragment",
        "type": ResourceTypes.MATERIAL,
        "icon": "🪸",
        "rarity": RARITY.UNCOMMON,
        get description() { return getText("Coral Fragment"); }},
    "Sponge": {
        "id": "Sponge",
        "name": "Sponge",
        "type": ResourceTypes.MATERIAL,
        "icon": "🧽",
        "rarity": RARITY.UNCOMMON,
        get description() { return getText("Sponge"); }},

    // Beach items
    "Shells": {
        "id": "Shells",
        "name": "Shells",
        "type": ResourceTypes.MATERIAL,
        "icon": "🐚",
        "rarity": RARITY.COMMON,
        "quantityMod": 3,
        get description() { return getText("Shells"); }},
    "CoconutShell": {
        "id": "CoconutShell",
        "name": "Coconut Shell",
        "type": ResourceTypes.MATERIAL,
        "icon": "🥥",
        "rarity": RARITY.COMMON,
        get description() { return getText("Coconut Shell"); }},

    // Forest items (for all forest biomes)
    "Pinecone": {
        "id": "Pinecone",
        "name": "Pinecone",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌲",
        "rarity": RARITY.COMMON,
        get description() { return getText("Pinecone"); }},
    "TreeSap": {
        "id": "TreeSap",
        "name": "Tree Sap",
        "type": ResourceTypes.MATERIAL,
        "icon": "🍯",
        "rarity": RARITY.UNCOMMON,
        get description() { return getText("Tree Sap"); }},
    "Moss": {
        "id": "Moss",
        "name": "Moss",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌿",
        "rarity": RARITY.COMMON,
        get description() { return getText("Moss"); }},
    "Feather": {
        "id": "Feather",
        "type": ResourceTypes.MATERIAL,
        "icon": "images/feather.png",
        "rarity": RARITY.UNCOMMON,
        get name() { return getText("Feather"); },
        get description() { return getText("Feather"); }},
    "Flower": {
        "id": "Flower",
        get name() { return getText("Flower"); },
        get description() { return getText("desc_Flowers"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "images/flower.svg",
        "rarity": RARITY.UNCOMMON
    },
    // Tropical Rain Forest specific
    "Cocoa": {
        "id": "Cocoa",
        "name": "Cocoa",
        "type": ResourceTypes.EDIBLE,
        "icon": "🍫",
        "rarity": RARITY.RARE,
        "food": 10,
        "energy": 15,
        get description() { return getText("Cocoa"); }},
    "Bamboo": {
        "id": "Bamboo",
        "name": "Bamboo",
        "type": ResourceTypes.MATERIAL,
        "icon": "images/bamboo.svg",
        "rarity": RARITY.UNCOMMON,
        get description() { return getText("Bamboo"); }
    },

    // Temperate Rain Forest specific
    "Truffle": {
        "id": "Truffle",
        "name": "Truffle",
        "type": ResourceTypes.EDIBLE,
        "icon": "🍄",
        "rarity": RARITY.RARE,
        "food": 15,
        "energy": 10,
        "health": 5,
        get description() { return getText("Truffle"); }},
    "Cedar": {
        "id": "Cedar",
        "name": "Cedar",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌲",
        "rarity": RARITY.UNCOMMON,
        "isFlammable": true,
        "burnTime": 35,
        get description() { return getText("Cedar"); }},

    // Temperate Deciduous Forest specific
    "Maple": {
        "id": "Maple",
        "name": "Maple",
        "type": ResourceTypes.MATERIAL,
        "icon": "🍁",
        "rarity": RARITY.UNCOMMON,
        get description() { return getText("Maple"); }},
    "Oak": {
        "id": "Oak",
        "name": "Oak",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌳",
        "rarity": RARITY.UNCOMMON,
        "isFlammable": true,
        "burnTime": 40,
        get description() { return getText("Oak"); }},
    "Beech": {
        "id": "Beech",
        "name": "Beech",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌳",
        "rarity": RARITY.UNCOMMON,
        get description() { return getText("Beech"); }},
    "MapleSyrup": {
        "id": "MapleSyrup",
        "name": "Maple Syrup",
        "type": ResourceTypes.EDIBLE,
        "icon": "🍯",
        "rarity": RARITY.RARE,
        "food": 5,
        "energy": 15,
        "sweetVal": 2,
        get description() { return getText("Maple Syrup"); }},

    // Tropical Seasonal Forest specific
    "Jackfruit": {
        "id": "Jackfruit",
        "name": "Jackfruit",
        "type": ResourceTypes.EDIBLE,
        "icon": "images/fruits/jackfruit.png",
        "rarity": RARITY.UNCOMMON,
        "food": 15,
        "water": 3,
        "energy": 1,
        "fruitVal": 1,
        get description() { return getText("Jackfruit"); }},

    // Taiga specific
    "Pine": {
        "id": "Pine",
        "name": "Pine",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌲",
        "rarity": RARITY.COMMON,
        "isFlammable": true,
        "burnTime": 30,
        get description() { return getText("Pine"); }},
    "Lichen": {
        "id": "Lichen",
        "name": "Lichen",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌿",
        "rarity": RARITY.COMMON,
        get description() { return getText("Lichen"); }},
    "Winterberries": {
        "id": "Winterberries",
        "name": "Winterberries",
        "type": ResourceTypes.EDIBLE,
        "icon": "🍒",
        "rarity": RARITY.UNCOMMON,
        "food": 6,
        "water": 4,
        "energy": 3,
        "fruitVal": 0.3,
        get description() { return getText("Winterberries"); }},

    // Shrubland specific
    "Shrub": {
        "id": "Shrub",
        "name": "Shrub",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌿",
        "rarity": RARITY.COMMON,
        "isFlammable": true,
        "burnTime": 20,
        get description() { return getText("Shrub"); }},
        
    // Grassland specific
    "TallGrass": {
        "id": "TallGrass",
        "name": "Tall Grass",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌾",
        "rarity": RARITY.COMMON,
        "isFlammable": true,
        "burnTime": 10,
        get description() { return getText("Tall Grass"); }},
    "Wheat": {
        "id": "Wheat",
        get name() { return getText("Wheat"); },
        get description() { return getText("desc_Wheat"); },
        "type": ResourceTypes.EDIBLE,
        "icon": "images/farming/wheat.png",
        "rarity": RARITY.LEGENDARY
    },
    "Dandelion": {
        "id": "Dandelion",
        "name": "Dandelion",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌼",
        "rarity": RARITY.COMMON,
        get description() { return getText("Dandelion"); }},
    "GrasslandRoots": {
        "id": "GrasslandRoots",
        "name": "Grassland Roots",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌱",
        "rarity": RARITY.COMMON,
        get description() { return getText("Grassland Roots"); }},

    // Ice Field specific
    "FrozenAlgae": {
        "id": "FrozenAlgae",
        "name": "Frozen Algae",
        "type": ResourceTypes.MATERIAL,
        "icon": "🧊",
        "rarity": RARITY.UNCOMMON,
        get description() { return getText("Frozen Algae"); }},
    "ArcticMoss": {
        "id": "ArcticMoss",
        "name": "Arctic Moss",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌿",
        "rarity": RARITY.RARE,
        get description() { return getText("Arctic Moss"); }},

    // Snow specific
    "PineCone": {
        "id": "PineCone",
        "name": "Pine Cone",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌲",
        "rarity": RARITY.COMMON,
        "isFlammable": true,
        "burnTime": 15,
        get description() { return getText("Pine Cone"); }},

    // Tundra specific
    "TundraLichen": {
        "id": "TundraLichen",
        "name": "Tundra Lichen",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌿",
        "rarity": RARITY.UNCOMMON,
        get description() { return getText("Tundra Lichen"); }},

    // Desert specific (both temperate and subtropical)
    "Cactus": {
        "id": "Cactus",
        "name": "Cactus",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌵",
        "rarity": RARITY.COMMON,
        get description() { return getText("Cactus"); }},
    "CactusFruit": {
        "id": "CactusFruit",
        "name": "Cactus Fruit",
        "type": ResourceTypes.EDIBLE,
        "icon": "🌵",
        "rarity": RARITY.UNCOMMON,
        "food": 8,
        "water": 15,
        "energy": 5,
        "fruitVal": 0.5,
        "waterVal": 1,
        get description() { return getText("Cactus Fruit"); }},
    "DesertRose": {
        "id": "DesertRose",
        "name": "Desert Rose",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌹",
        "rarity": RARITY.RARE,
        get description() { return getText("Desert Rose"); }},
    "Scorpion": {
        "id": "Scorpion",
        "name": "Scorpion",
        "type": ResourceTypes.MATERIAL,
        "icon": "🦂",
        "rarity": RARITY.UNCOMMON,
        get description() { return getText("Scorpion"); }},
    "DesertLizard": {
        "id": "DesertLizard",
        "name": "Desert Lizard",
        "type": ResourceTypes.EDIBLE,
        "icon": "🦎",
        "rarity": RARITY.UNCOMMON,
        "food": 10,
        "energy": 5,
        "health": 2,
        get description() { return getText("Desert Lizard"); }},

    // Volcanic Wasteland specific
    "Obsidian": {
        "id": "Obsidian",
        "name": "Obsidian",
        "type": ResourceTypes.MATERIAL,
        "icon": "⬛",
        "rarity": RARITY.RARE,
        get description() { return getText("Obsidian"); }},
    "Sulfur": {
        "id": "Sulfur",
        "name": "Sulfur",
        "type": ResourceTypes.MATERIAL,
        "icon": "💛",
        "rarity": RARITY.UNCOMMON,
        get description() { return getText("Sulfur"); }},
    "VolcanicRock": {
        "id": "VolcanicRock",
        "name": "Volcanic Rock",
        "type": ResourceTypes.MATERIAL,
        "icon": "🪨",
        "rarity": RARITY.COMMON,
        get description() { return getText("Volcanic Rock"); }},
    "VolcanicAsh": {
        "id": "VolcanicAsh",
        "name": "Volcanic Ash",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌋",
        "rarity": RARITY.COMMON,
        get description() { return getText("Volcanic Ash"); }},
    "HeatResistantMoss": {
        "id": "HeatResistantMoss",
        "name": "Heat Resistant Moss",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌿",
        "rarity": RARITY.RARE,
        get description() { return getText("Heat Resistant Moss"); }},

    // Wasteland specific
    "DeadWood": {
        "id": "DeadWood",
        "name": "Dead Wood",
        "type": ResourceTypes.MATERIAL,
        "icon": "🪵",
        "rarity": RARITY.COMMON,
        "isFlammable": true,
        "burnTime": 20,
        get description() { return getText("Dead Wood"); }},
    "WastelandRoot": {
        "id": "WastelandRoot",
        "name": "Wasteland Root",
        "type": ResourceTypes.EDIBLE,
        "icon": "🌱",
        "rarity": RARITY.UNCOMMON,
        "food": 5,
        "energy": 2,
        get description() { return getText("Wasteland Root"); }},
    "RustedMetal": {
        "id": "RustedMetal",
        "name": "Rusted Metal",
        "type": ResourceTypes.MATERIAL,
        "icon": "🔧",
        "rarity": RARITY.UNCOMMON,
        get description() { return getText("Rusted Metal"); }},
    "WastelandBerry": {
        "id": "WastelandBerry",
        "name": "Wasteland Berry",
        "type": ResourceTypes.EDIBLE,
        "icon": "🍇",
        "rarity": RARITY.RARE,
        "food": 3,
        "water": 2,
        "energy": 1,
        "health": -1,
        "fruitVal": 0.1,
        get description() { return getText("Wasteland Berry"); }},

    // Lake specific
    "Cattail": {
        "id": "Cattail",
        "name": "Cattail",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌾",
        "rarity": RARITY.COMMON,
        get description() { return getText("Cattail"); }},
    "LotusRoot": {
        "id": "LotusRoot",
        "name": "Lotus Root",
        "type": ResourceTypes.EDIBLE,
        "icon": "🌱",
        "rarity": RARITY.UNCOMMON,
        "food": 10,
        "water": 5,
        "energy": 5,
        "health": 3,
        get description() { return getText("Lotus Root"); }},

    // Marsh specific
    "MarshReed": {
        "id": "MarshReed",
        "name": "Marsh Reed",
        "type": ResourceTypes.MATERIAL,
        "icon": "🌿",
        "rarity": RARITY.COMMON,
        get description() { return getText("Marsh Reed"); }},
    "Leech": {
        "id": "Leech",
        "name": "Leech",
        "type": ResourceTypes.MATERIAL,
        "icon": "🪱",
        "rarity": RARITY.UNCOMMON,
        get description() { return getText("Leech"); }
    },
};


// for (const [key, value] of Object.entries(Items)) {
//     value.id = key;
// }
// console.log("Items", Items);


const AlwaysAvailableResourcesInForests = [Items.Log, Items.Twig, Items.Leaf, MATERIALS.Grass,];

export const AlwaysAvailableResourcesBiomeMap = {
    "OCEAN": [Items.Seaweed,  Items.Plankton],
    "BEACH": [Items.Sand, Items.Stone, Items.Shells],
    // Lake
    "LAKE": [ ],
    // Marsh
    "MARSH": [ Items.MarshReed],

    // 热带雨林
    TROPICAL_RAIN_FOREST: [...AlwaysAvailableResourcesInForests],
    // 温带雨林
    "TEMPERATE_RAIN_FOREST": [...AlwaysAvailableResourcesInForests],
    // 温带落叶森林
    "TEMPERATE_DECIDUOUS_FOREST": [...AlwaysAvailableResourcesInForests, Items.Maple, Items.Oak],
    // 热带季节性森林
    "TROPICAL_SEASONAL_FOREST": [...AlwaysAvailableResourcesInForests],
    // 针叶林
    "TAIGA": [...AlwaysAvailableResourcesInForests, Items.Pine],
    // 灌木林
    "SHRUBLAND": [...AlwaysAvailableResourcesInForests, Items.Shrub],
    // 草地
    "GRASSLAND": [Items.Grass, Items.TallGrass, Items.Stone, Items.Dandelion],

    // 冰地
    "ICEFIELD": [Items.Ice, Items.Stone, ],
    // 雪地
    "SNOWFIELD": [Items.Stone, Items.Ice],
    // 苔原
    "TUNDRA": [Items.Stone, Items.Ice,],

    // "温带沙漠
    "TEMPERATE_DESERT": [Items.Stone, Items.Sand, Items.Cactus],
    // 亚热带沙漠
    "SUBTROPICAL_DESERT": [Items.Stone, Items.Sand, Items.Cactus],
    // 焦土
    "VOLCANIC_WASTELAND": [Items.Stone, Items.Flint, Items.VolcanicRock, Items.VolcanicAsh],
    // 荒地
    "WASTELAND": [Items.Stone,]
};


/**
 * @type {{ [key: string]: Item[]}}
 */
export const BiomeResources = {
    OCEAN: [
        Items.Seaweed, Items.Coral, Items.Pearl, Items.FishBone,
        Items.Starfish, Items.Jellyfish,
        Items.SeaGlass, Items.Kelp, Items.OceanSalt, Items.Plankton,
        Items.Ambergris, Items.Barnacles, Items.SeaUrchin, Items.CoralFragment,
        Items.Sponge, Items.Tuna, Items.Mackerel, Items.Sardine,
        Items.Squid, Items.Swordfish, Items["Giant Octopus"], Items.Kraken
    ],
    BEACH: [
        Items.Coconut, Items.SeaCucumber,
        Items.Crab, Items.Leaf, Items.Twig, Items.Sand,
        Items.AloeVera, Items.Shells,
        Items.Pineapple, Items.TurtleEgg,
        Items.Clam, FOOD.Plum, BUILDINGS.CoconutTree
    ],
    // 湖泊
    "LAKE": [
        Items.Cattail,
        Items.LotusRoot, 
    ],
    // 沼泽
    "MARSH": [
        Items.MarshReed, Items.Leech,
        Items.Grass, Items.Moss,
        Items.Flower
    ],

    // 热带雨林
    TROPICAL_RAIN_FOREST: [
        Items.Banana, Items.Log, Items.Leaf,
        Items.Twig, Items.Stone, Items.Cocoa,
        Items.Papaya, Items.Bamboo, 
        Items.TreeSap, Items.Moss,
        Items.Feather, Items.Flower, MUSHROOMS.OysterMushroom, MUSHROOMS.ShiitakeMushroom
    ],
    // 热带季节性森林
    "TROPICAL_SEASONAL_FOREST": [
        Items.Log, Items.Leaf, Items.Twig,
        Items.Stone, Items.Jackfruit,
        Items.TreeSap, Items.Moss, Items.Feather,
        Items.Flower,
        Items.Banana, Items.Papaya, Items.Mango, Items.Bamboo
    ],
    // 温带雨林
    "TEMPERATE_RAIN_FOREST": [
        Items.Log, Items.Leaf, Items.Twig, Items.Stone,
        Items.Truffle, Items.Cedar,
        Items.TreeSap, Items.Moss, Items.Feather,
        Items.Flower,
        Items.Apple, 
        MUSHROOMS.Chanterelle, MUSHROOMS.OysterMushroom, MUSHROOMS.ShiitakeMushroom,
        MUSHROOMS.FlyAgaric, VEGETABLES.Beet
    ],
    // 温带落叶森林
    "TEMPERATE_DECIDUOUS_FOREST": [
        Items.Log, Items.Leaf, Items.Twig, Items.Stone,
        Items.Maple, Items.Oak, Items.Beech, Items.MapleSyrup,
        Items.TreeSap, Items.Moss,
        Items.Feather, Items.Flower,
        Items.Apple, Items.Acorn, Items.Grass,
        MUSHROOMS.Chanterelle, MUSHROOMS.OysterMushroom, MUSHROOMS.ButtonMushroom,
        MUSHROOMS.Morel, MUSHROOMS.FalseMorel, VEGETABLES.Beet 
    ],
    // 针叶林
    "TAIGA": [
        Items.Log, Items.Leaf, Items.Twig, Items.Stone,
        Items.Pine, Items.Lichen, Items.Winterberries,
        Items.TreeSap, Items.Moss,
        Items.Feather, Items.Flower,
        Items.Pinecone, Items.Grass, Items.PineCone,
        MUSHROOMS.FlyAgaric, MUSHROOMS.FalseMorel, MUSHROOMS.Chanterelle, MUSHROOMS.OysterMushroom, MUSHROOMS.Morel
    ],
    // 灌木林
    "SHRUBLAND": [
        Items.Log, Items.Leaf, Items.Twig, Items.Stone,
        Items.Shrub,
        Items.Moss, Items.Feather,
        Items.Flower, Items.Grass,
        Items.Dandelion, Items.Acorn, Items.Apple, MUSHROOMS.ButtonMushroom, MUSHROOMS.Morel
    ],
    // 草地
    "GRASSLAND": [
        Items.Grass, Items.TallGrass, Items.Wheat, Items.Dandelion,
        Items.GrasslandRoots, Items.Carrot, Items.Potato, Items.Stone,
        Items.Moss, Items.Feather,
        Items.Flower, Items.Twig,
        Items.Leaf, Items.Log, Items.Acorn
    ],

    // 冰地
    "ICEFIELD": [
        Items.Ice,  Items.FrozenAlgae, Items.ArcticMoss,
        Items.Stone, Items.PineCone, Items.TundraLichen, 
        Items.Lichen, Items.Moss, Items.Feather,
        Items.Flower
    ],
    // 雪地
    "SNOWFIELD": [
        Items.Ice, Items.Stone,
        Items.PineCone, Items.TundraLichen,
         Items.Lichen, Items.Moss,
        Items.Feather, Items.Flower,
        Items.Log, Items.Twig
    ],
    // 苔原
    "TUNDRA": [
        Items.Stone, Items.Ice, 
        Items.TundraLichen,  Items.Lichen, Items.Moss,
        Items.Feather,
        Items.Flower, Items.Log,
        Items.Twig, Items.Leaf, Items.PineCone,
        MUSHROOMS.FlyAgaric
    ],

    // "温带沙漠
    "TEMPERATE_DESERT": [
        Items.Stone, Items.Cactus, Items.CactusFruit, Items.DesertRose,
        Items.Scorpion, Items.DesertLizard,
        Items.Sand,  Items.Twig, Items.Leaf,
        Items.Grass, Items.Moss, Items.Feather,
        Items.Flower
    ],
    // 亚热带沙漠
    "SUBTROPICAL_DESERT": [
        Items.Stone, Items.Cactus, Items.CactusFruit, Items.DesertRose,
        Items.Scorpion, Items.DesertLizard,
        Items.Sand,  Items.Twig, Items.Leaf,
        Items.Grass, Items.Moss, Items.Feather,
        Items.Flower
    ],
    // 焦土
    "VOLCANIC_WASTELAND": [
        Items.Stone, Items.Obsidian, Items.Sulfur, Items.VolcanicRock,
        Items.VolcanicAsh, Items.HeatResistantMoss,  Items.Twig,
        Items.Leaf, Items.Grass, Items.Moss,
        Items.Feather, Items.Flower,
        Items.Log, Items.Bark
    ],
    // 荒地
    "WASTELAND": [
        Items.WastelandRoot, Items.RustedMetal,
        Items.WastelandBerry, Items.Stone,  Items.Twig,
        Items.Leaf, Items.Grass, Items.Moss,
        Items.Feather, Items.Flower,
        Items.Log, Items.Bark
    ]
};

export const BIOMES = {
    OCEAN: {
        get name() { return getText("Ocean"); },
        get description() { return getText("desc_Ocean"); }
    },
    BEACH: {
        get name() { return getText("Beach"); },
        get description() { return getText("desc_Beach"); }
    },
    LAKE: {
        get name() { return getText("Lake"); },
        get description() { return getText("desc_Lake"); }
    },
    MARSH: {
        get name() { return getText("Marsh"); },
        get description() { return getText("desc_Marsh"); },
        temperatureMod: 5
    },
    TROPICAL_RAIN_FOREST: {
        get name() { return getText("Tropical Rain Forest"); },
        get description() { return getText("desc_Tropical Rain Forest"); },
        temperatureMod: 15,
        rainMod: 2
    },
    TEMPERATE_RAIN_FOREST: {
        get name() { return getText("Temperate Rain Forest"); },
        get description() { return getText("desc_Temperate Rain Forest"); },
        rainMod: 2
    },
    TEMPERATE_DECIDUOUS_FOREST: {
        get name() { return getText("Temperate Deciduous Forest"); },
        get description() { return getText("desc_Temperate Deciduous Forest"); }
    },
    TROPICAL_SEASONAL_FOREST: {
        get name() { return getText("Tropical Seasonal Forest"); },
        get description() { return getText("desc_Tropical Seasonal Forest"); },
        temperatureMod: 15,
    },
    TAIGA: {
        get name() { return getText("Taiga"); },
        get description() { return getText("desc_Taiga"); },
        temperatureMod: -15
    },
    SHRUBLAND: {
        get name() { return getText("Shrubland"); },
        get description() { return getText("desc_Shrubland"); },
        temperatureMod: -15
    },
    GRASSLAND: {
        get name() { return getText("Grassland"); },
        get description() { return getText("desc_Grassland"); },
        temperatureMod: 5,
    },


    ICEFIELD: {
        get name() { return getText("Icefield"); },
        get description() { return getText("desc_Icefield"); },
        temperatureMod: -40
    },
    SNOWFIELD: {
        get name() { return getText("Snowfield"); },
        get description() { return getText("desc_Snowfield"); },
        temperatureMod: -30
    },
    TUNDRA: {
        get name() { return getText("Tundra"); },
        get description() { return getText("desc_Tundra"); },
        temperatureMod: -30
    },

    TEMPERATE_DESERT: {
        get name() { return getText("Temperate Desert"); },
        get description() { return getText("desc_Temperate Desert"); },
        temperatureMod: 5,
        nightTemperatureMod: -15
    },
    SUBTROPICAL_DESERT: {
        get name() { return getText("Subtropical Desert"); },
        get description() { return getText("desc_Subtropical Desert"); },
        temperatureMod: 15,
        nightTemperatureMod: -15
    },
    VOLCANIC_WASTELAND: {
        get name() { return getText("Volcanic Wasteland"); },
        get description() { return getText("desc_Volcanic Wasteland"); },
        temperatureMod: 10,
    },
    WASTELAND: {
        get name() { return getText("Wasteland"); },
        get description() { return getText("desc_Wasteland"); },
        temperatureMod: -5
    }


}

console.log("BiomeResources", BiomeResources);

// Helper function to init random resources for each region of each biome at the start of the game
export function getRandomResources(biome: string, count = 15): ResourceMetaInfo[] {
    const resources = BiomeResources[biome] || [];
    const availableResources: ResourceMetaInfo[] = resources
        .filter((_: Item) => Math.random() > 0.5)
        .map((r: Item) => {
            try {
                // discovered: true is for debug!!
                return {"id": r.id, "discovered": true};
            } catch (error) {
                console.log("getRandomResources", biome, r, error);
                return null;
            }
        })
        .filter((item: ResourceMetaInfo | null): item is ResourceMetaInfo => item !== null);

    if (AlwaysAvailableResourcesBiomeMap[biome]) {
        AlwaysAvailableResourcesBiomeMap[biome]
            .filter((resource: Item) => !availableResources.find((r: ResourceMetaInfo) => r.id === resource.id))
            .forEach((r: Item) => {
                availableResources.push({"id": r.id, "discovered": true});
            });
    }

    // If we have fewer items than requested count, add more random items
    if (availableResources.length < count && resources.length > 0) {
        const remainingItems = resources.filter((item: Item) =>
            !availableResources.find((r: ResourceMetaInfo) => r.id === item.id));

        // Shuffle the remaining items
        const shuffled = [...remainingItems].sort(() => Math.random() - 0.5);

        // Add items until we reach the count or run out of items
        for (let i = 0; i < Math.min(count - availableResources.length, shuffled.length); i++) {
            availableResources.push({"id": shuffled[i].id, "discovered": true});
        }
    }

    return availableResources;
}



// "OCEAN"
// "LAKE"
// "BEACH"
// "MARSH"
// "TROPICAL_RAIN_FOREST"
// "TEMPERATE_RAIN_FOREST"
// "TEMPERATE_DECIDUOUS_FOREST"
// "TROPICAL_SEASONAL_FOREST"
// "TAIGA"
// "SHRUBLAND"
// "GRASSLAND"
// "ICEFIELD"
// "SNOWFIELD"
// "TUNDRA"
// "TEMPERATE_DESERT"
// "SUBTROPICAL_DESERT"
// "VOLCANIC_WASTELAND"
// "WASTELAND"